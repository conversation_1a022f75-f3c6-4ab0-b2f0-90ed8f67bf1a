const {
    <PERSON>dal<PERSON><PERSON>er,
    TextInputBuilder,
    TextInputStyle,
    ActionRowBuilder,
    EmbedBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    MessageFlags
} = require('discord.js');
const reactionRoleStorage = require('./reactionRoleStorage');
const reactionRoleValidator = require('./reactionRoleValidator');

class SetupWizardHandler {
    /**
     * Check if an interaction is a setup wizard interaction
     * @param {string} customId - The custom ID to check
     * @returns {boolean} True if it's a setup wizard interaction
     */
    isSetupWizardInteraction(customId) {
        return customId.startsWith('setup_');
    }

    /**
     * Handle setup wizard interactions
     * @param {ButtonInteraction|StringSelectMenuInteraction|ModalSubmitInteraction} interaction - The interaction
     */
    async handleSetupWizardInteraction(interaction) {
        const customId = interaction.customId;
        const sessionId = this.extractSessionId(customId);
        
        if (!sessionId) {
            return interaction.reply({
                content: '❌ Invalid setup session.',
                flags: MessageFlags.Ephemeral
            });
        }

        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (!session) {
            return interaction.reply({
                content: '❌ Setup session expired or not found. Please start a new setup.',
                flags: MessageFlags.Ephemeral
            });
        }

        // Verify the user owns this session
        if (session.userId !== interaction.user.id) {
            return interaction.reply({
                content: '❌ This setup session belongs to another user.',
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            if (customId.includes('_title_')) {
                await this.handleTitleSetup(interaction, sessionId);
            } else if (customId.includes('_description_')) {
                await this.handleDescriptionSetup(interaction, sessionId);
            } else if (customId.includes('_mode_')) {
                await this.handleModeSetup(interaction, sessionId);
            } else if (customId.includes('_roles_')) {
                await this.handleRolesSetup(interaction, sessionId);
            } else if (customId.includes('_image_')) {
                await this.handleImageSetup(interaction, sessionId);
            } else if (customId.includes('_preview_')) {
                await this.handlePreview(interaction, sessionId);
            } else if (customId.includes('_create_')) {
                await this.handleCreatePanel(interaction, sessionId);
            } else if (customId.includes('_cancel_')) {
                await this.handleCancel(interaction, sessionId);
            } else if (customId.includes('_mode_select_')) {
                await this.handleModeSelection(interaction, sessionId);
            } else if (customId.includes('_title_modal_') || customId.includes('_description_modal_') || 
                      customId.includes('_image_modal_') || customId.includes('_roles_modal_')) {
                await this.handleModalSubmit(interaction, sessionId);
            }
        } catch (error) {
            console.error('Error handling setup wizard interaction:', error);
            await interaction.reply({
                content: `❌ An error occurred: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }

    /**
     * Extract session ID from custom ID
     * @param {string} customId - The custom ID
     * @returns {string|null} Session ID or null if not found
     */
    extractSessionId(customId) {
        const parts = customId.split('_');
        if (parts.length >= 3) {
            return parts.slice(2).join('_');
        }
        return null;
    }

    /**
     * Handle title setup
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleTitleSetup(interaction, sessionId) {
        const modal = new ModalBuilder()
            .setCustomId(`setup_title_modal_${sessionId}`)
            .setTitle('Set Panel Title');

        const titleInput = new TextInputBuilder()
            .setCustomId('title')
            .setLabel('Panel Title (max 256 characters)')
            .setStyle(TextInputStyle.Short)
            .setMaxLength(256)
            .setRequired(true)
            .setPlaceholder('Enter the title for your reaction role panel...');

        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (session && session.title) {
            titleInput.setValue(session.title);
        }

        const actionRow = new ActionRowBuilder().addComponents(titleInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    }

    /**
     * Handle description setup
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleDescriptionSetup(interaction, sessionId) {
        const modal = new ModalBuilder()
            .setCustomId(`setup_description_modal_${sessionId}`)
            .setTitle('Set Panel Description');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('description')
            .setLabel('Panel Description (max 4096 characters)')
            .setStyle(TextInputStyle.Paragraph)
            .setMaxLength(4096)
            .setRequired(true)
            .setPlaceholder('Enter the description for your reaction role panel...');

        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (session && session.description) {
            descriptionInput.setValue(session.description);
        }

        const actionRow = new ActionRowBuilder().addComponents(descriptionInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    }

    /**
     * Handle mode setup
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleModeSetup(interaction, sessionId) {
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`setup_mode_select_${sessionId}`)
            .setPlaceholder('Choose role assignment mode')
            .addOptions([
                {
                    label: 'Multiple Roles',
                    description: 'Users can have multiple roles from this panel',
                    value: 'multi',
                    emoji: '🔢'
                },
                {
                    label: 'Single Role',
                    description: 'Users can only have one role from this panel',
                    value: 'single',
                    emoji: '1️⃣'
                }
            ]);

        const actionRow = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            content: '🔧 **Select Role Assignment Mode:**\n\n' +
                     '**Multiple Roles:** Users can select and keep multiple roles from this panel.\n' +
                     '**Single Role:** Users can only have one role from this panel at a time.',
            components: [actionRow],
            flags: MessageFlags.Ephemeral
        });
    }

    /**
     * Handle mode selection from dropdown
     * @param {StringSelectMenuInteraction} interaction - The select menu interaction
     * @param {string} sessionId - Session ID
     */
    async handleModeSelection(interaction, sessionId) {
        const selectedMode = interaction.values[0];
        
        reactionRoleStorage.updateSetupSession(sessionId, { mode: selectedMode });

        await interaction.update({
            content: `✅ Mode set to: **${selectedMode === 'multi' ? 'Multiple Roles' : 'Single Role'}**`,
            components: []
        });

        // Update the main wizard embed
        setTimeout(async () => {
            try {
                const setupCommand = require('../commands/setupReactionRoles');
                const session = reactionRoleStorage.getSetupSession(sessionId);
                const channel = interaction.guild.channels.cache.get(session.channelId);
                const wizardContent = setupCommand.createSetupWizardEmbed(sessionId, channel);
                
                await interaction.followUp({
                    ...wizardContent,
                    flags: MessageFlags.Ephemeral
                });
            } catch (error) {
                console.error('Error updating wizard embed:', error);
            }
        }, 1000);
    }

    /**
     * Handle roles setup
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleRolesSetup(interaction, sessionId) {
        const modal = new ModalBuilder()
            .setCustomId(`setup_roles_modal_${sessionId}`)
            .setTitle('Add Roles');

        const rolesInput = new TextInputBuilder()
            .setCustomId('roles')
            .setLabel('Role Configuration')
            .setStyle(TextInputStyle.Paragraph)
            .setRequired(true)
            .setPlaceholder('Format: role_id:label,role_id:label\nExample: 123456789:Gamer,987654321:Student');

        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (session && session.roles.length > 0) {
            const rolesString = session.roles.map(role =>
                `${role.role_id}:${role.label}`
            ).join(',');
            rolesInput.setValue(rolesString);
        }

        const actionRow = new ActionRowBuilder().addComponents(rolesInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    }

    /**
     * Handle image setup
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleImageSetup(interaction, sessionId) {
        const modal = new ModalBuilder()
            .setCustomId(`setup_image_modal_${sessionId}`)
            .setTitle('Set Panel Image (Optional)');

        const imageInput = new TextInputBuilder()
            .setCustomId('image_url')
            .setLabel('Image URL (optional)')
            .setStyle(TextInputStyle.Short)
            .setRequired(false)
            .setPlaceholder('https://example.com/image.png');

        const session = reactionRoleStorage.getSetupSession(sessionId);
        if (session && session.imageUrl) {
            imageInput.setValue(session.imageUrl);
        }

        const actionRow = new ActionRowBuilder().addComponents(imageInput);
        modal.addComponents(actionRow);

        await interaction.showModal(modal);
    }

    /**
     * Handle modal submissions
     * @param {ModalSubmitInteraction} interaction - The modal submit interaction
     * @param {string} sessionId - Session ID
     */
    async handleModalSubmit(interaction, sessionId) {
        const customId = interaction.customId;

        if (customId.includes('_title_modal_')) {
            const title = interaction.fields.getTextInputValue('title');

            // Validate title
            const validation = reactionRoleValidator.validateTitle(title);
            if (!validation.success) {
                return interaction.reply({
                    content: `❌ ${validation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            reactionRoleStorage.updateSetupSession(sessionId, { title });

            await interaction.reply({
                content: `✅ Title set to: **${title}**`,
                flags: MessageFlags.Ephemeral
            });

        } else if (customId.includes('_description_modal_')) {
            const description = interaction.fields.getTextInputValue('description');

            // Validate description
            const validation = reactionRoleValidator.validateDescription(description);
            if (!validation.success) {
                return interaction.reply({
                    content: `❌ ${validation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            reactionRoleStorage.updateSetupSession(sessionId, { description });

            await interaction.reply({
                content: `✅ Description set successfully!`,
                flags: MessageFlags.Ephemeral
            });

        } else if (customId.includes('_image_modal_')) {
            const imageUrl = interaction.fields.getTextInputValue('image_url');

            // Validate image URL if provided
            if (imageUrl) {
                const validation = reactionRoleValidator.validateImageUrl(imageUrl);
                if (!validation.success) {
                    return interaction.reply({
                        content: `❌ ${validation.error}`,
                        flags: MessageFlags.Ephemeral
                    });
                }
            }

            reactionRoleStorage.updateSetupSession(sessionId, { imageUrl: imageUrl || null });

            await interaction.reply({
                content: imageUrl ? `✅ Image URL set successfully!` : `✅ Image URL cleared.`,
                flags: MessageFlags.Ephemeral
            });

        } else if (customId.includes('_roles_modal_')) {
            const rolesString = interaction.fields.getTextInputValue('roles');

            // Validate roles string
            const validation = reactionRoleValidator.validateRolesString(rolesString, interaction.guild);
            if (!validation.success) {
                return interaction.reply({
                    content: `❌ ${validation.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Parse roles
            const parseResult = await this.parseRolesString(rolesString, interaction.guild);
            if (!parseResult.success) {
                return interaction.reply({
                    content: `❌ ${parseResult.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Validate role hierarchy
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const hierarchyCheck = reactionRoleValidator.validateRoleHierarchy(parseResult.roles, botMember);
            if (!hierarchyCheck.success) {
                return interaction.reply({
                    content: `❌ ${hierarchyCheck.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            reactionRoleStorage.updateSetupSession(sessionId, { roles: parseResult.roles });

            await interaction.reply({
                content: `✅ ${parseResult.roles.length} role(s) configured successfully!`,
                flags: MessageFlags.Ephemeral
            });
        }

        // Update the main wizard embed after a short delay
        setTimeout(async () => {
            try {
                const setupCommand = require('../commands/setupReactionRoles');
                const session = reactionRoleStorage.getSetupSession(sessionId);
                const channel = interaction.guild.channels.cache.get(session.channelId);
                const wizardContent = setupCommand.createSetupWizardEmbed(sessionId, channel);

                await interaction.followUp({
                    ...wizardContent,
                    flags: MessageFlags.Ephemeral
                });
            } catch (error) {
                console.error('Error updating wizard embed:', error);
            }
        }, 1000);
    }

    /**
     * Parse roles string into role configurations
     * @param {string} rolesString - The roles string to parse
     * @param {Guild} guild - The Discord guild
     * @returns {Promise<Object>} Parse result with success status and data/error
     */
    async parseRolesString(rolesString, guild) {
        try {
            const rolePairs = rolesString.split(',').map(pair => pair.trim());
            const roles = [];

            for (const pair of rolePairs) {
                const parts = pair.split(':');
                if (parts.length !== 2) {
                    return {
                        success: false,
                        error: `Invalid format for role pair: "${pair}". Expected format: role_id:label`
                    };
                }

                const [roleId, label] = parts.map(part => part.trim());

                // Validate role exists
                const role = guild.roles.cache.get(roleId);
                if (!role) {
                    return {
                        success: false,
                        error: `Role with ID "${roleId}" not found in this server.`
                    };
                }

                // Validate label
                if (!label || label.length > 80) {
                    return {
                        success: false,
                        error: `Invalid label: "${label}". Label must be 1-80 characters long.`
                    };
                }

                roles.push({
                    role_id: roleId,
                    label: label
                });
            }

            if (roles.length === 0) {
                return {
                    success: false,
                    error: 'At least one role must be specified.'
                };
            }

            if (roles.length > 25) {
                return {
                    success: false,
                    error: 'Maximum of 25 roles allowed per panel (Discord button limit).'
                };
            }

            return {
                success: true,
                roles: roles
            };

        } catch (error) {
            return {
                success: false,
                error: `Error parsing roles: ${error.message}`
            };
        }
    }

    /**
     * Handle preview functionality
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handlePreview(interaction, sessionId) {
        const session = reactionRoleStorage.getSetupSession(sessionId);

        // Create preview embed
        const previewEmbed = new EmbedBuilder()
            .setTitle(session.title)
            .setDescription(session.description)
            .setColor(0x5865F2);

        if (session.imageUrl) {
            previewEmbed.setImage(session.imageUrl);
        }

        // Create preview buttons (up to 25 roles, 5 per row)
        const components = [];
        const roles = session.roles;

        for (let i = 0; i < roles.length; i += 5) {
            const row = new ActionRowBuilder();
            const rowRoles = roles.slice(i, i + 5);

            for (const role of rowRoles) {
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId(`preview_role_${role.role_id}`)
                        .setLabel(role.label)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true) // Disabled for preview
                );
            }

            components.push(row);
        }

        await interaction.reply({
            content: '👁️ **Preview of your reaction role panel:**\n*This is how it will look when created (buttons are disabled in preview)*',
            embeds: [previewEmbed],
            components: components,
            flags: MessageFlags.Ephemeral
        });
    }

    /**
     * Handle panel creation
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleCreatePanel(interaction, sessionId) {
        const session = reactionRoleStorage.getSetupSession(sessionId);
        const channel = interaction.guild.channels.cache.get(session.channelId);

        try {
            // Create the reaction role panel
            const panelData = {
                title: session.title,
                description: session.description,
                mode: session.mode,
                roles: session.roles,
                image_url: session.imageUrl
            };

            // Use existing reaction role creation logic
            const reactionRoleHandler = require('./reactionRoleHandler');
            const result = await reactionRoleHandler.createReactionRolePanel(
                channel,
                panelData,
                interaction.guild
            );

            if (result.success) {
                // Clean up the setup session
                reactionRoleStorage.deleteSetupSession(sessionId);

                await interaction.reply({
                    content: `✅ **Reaction role panel created successfully!**\n\n` +
                             `📍 **Channel:** ${channel}\n` +
                             `📝 **Title:** ${session.title}\n` +
                             `👥 **Roles:** ${session.roles.length} role(s)\n` +
                             `⚙️ **Mode:** ${session.mode === 'multi' ? 'Multiple roles' : 'Single role'}\n\n` +
                             `The panel is now live and users can interact with it!`,
                    flags: MessageFlags.Ephemeral
                });
            } else {
                await interaction.reply({
                    content: `❌ Failed to create panel: ${result.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

        } catch (error) {
            console.error('Error creating reaction role panel:', error);
            await interaction.reply({
                content: `❌ An error occurred while creating the panel: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }

    /**
     * Handle setup cancellation
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {string} sessionId - Session ID
     */
    async handleCancel(interaction, sessionId) {
        // Clean up the setup session
        reactionRoleStorage.deleteSetupSession(sessionId);

        await interaction.reply({
            content: '❌ **Setup cancelled.**\n\nYour configuration has been discarded. You can start a new setup anytime with `/setup-reaction-roles`.',
            flags: MessageFlags.Ephemeral
        });
    }
}

module.exports = new SetupWizardHandler();
