const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const reactionRoleStorage = require('./reactionRoleStorage');

class ReactionRoleRecovery {
    /**
     * Initialize reaction role recovery on bot startup
     * @param {Client} client - Discord client instance
     */
    async initializeRecovery(client) {
        console.log('🔄 Starting reaction role recovery...');
        
        try {
            const allPanels = await reactionRoleStorage.getAllPanels();
            const panelEntries = Object.entries(allPanels);
            
            if (panelEntries.length === 0) {
                console.log('✅ No reaction role panels to recover.');
                return;
            }

            console.log(`📋 Found ${panelEntries.length} reaction role panels to verify...`);

            let recoveredCount = 0;
            let recreatedCount = 0;
            let removedCount = 0;

            for (const [panelId, panelData] of panelEntries) {
                try {
                    const result = await this.verifyAndRecoverPanel(client, panelId, panelData);
                    
                    switch (result.status) {
                        case 'recovered':
                            recoveredCount++;
                            break;
                        case 'recreated':
                            recreatedCount++;
                            break;
                        case 'removed':
                            removedCount++;
                            break;
                    }
                } catch (error) {
                    console.error(`❌ Error processing panel ${panelId}:`, error);
                }
            }

            console.log(`✅ Reaction role recovery complete:`);
            console.log(`   - Recovered: ${recoveredCount} panels`);
            console.log(`   - Recreated: ${recreatedCount} panels`);
            console.log(`   - Removed: ${removedCount} panels`);

        } catch (error) {
            console.error('❌ Error during reaction role recovery:', error);
        }
    }

    /**
     * Verify and recover a single panel
     * @param {Client} client - Discord client instance
     * @param {string} panelId - Panel ID
     * @param {Object} panelData - Panel configuration data
     * @returns {Promise<Object>} Recovery result
     */
    async verifyAndRecoverPanel(client, panelId, panelData) {
        try {
            // Get guild
            const guild = client.guilds.cache.get(panelData.guild_id);
            if (!guild) {
                console.log(`⚠️  Guild ${panelData.guild_id} not found for panel ${panelId}, removing panel`);
                await reactionRoleStorage.removePanel(panelId);
                return { status: 'removed', reason: 'Guild not found' };
            }

            // Get channel
            const channel = guild.channels.cache.get(panelData.channel_id);
            if (!channel) {
                console.log(`⚠️  Channel ${panelData.channel_id} not found for panel ${panelId}, removing panel`);
                await reactionRoleStorage.removePanel(panelId);
                return { status: 'removed', reason: 'Channel not found' };
            }

            // Try to get the message
            let message = null;
            try {
                message = await channel.messages.fetch(panelData.message_id);
            } catch (error) {
                console.log(`⚠️  Message ${panelData.message_id} not found for panel ${panelId}, attempting to recreate`);
            }

            // Verify roles still exist
            const validRoles = [];
            for (const roleConfig of panelData.roles) {
                const role = guild.roles.cache.get(roleConfig.role_id);
                if (role) {
                    validRoles.push(roleConfig);
                } else {
                    console.log(`⚠️  Role ${roleConfig.role_id} no longer exists for panel ${panelId}`);
                }
            }

            // If no valid roles remain, remove the panel
            if (validRoles.length === 0) {
                console.log(`⚠️  No valid roles remain for panel ${panelId}, removing panel`);
                if (message) {
                    try {
                        await message.delete();
                    } catch (error) {
                        console.log(`Could not delete message for panel ${panelId}:`, error.message);
                    }
                }
                await reactionRoleStorage.removePanel(panelId);
                return { status: 'removed', reason: 'No valid roles' };
            }

            // Update panel data if roles were removed
            if (validRoles.length !== panelData.roles.length) {
                await reactionRoleStorage.updatePanel(panelId, { roles: validRoles });
                panelData.roles = validRoles;
            }

            // If message exists and is valid, recovery complete
            if (message && this.validateMessage(message, panelData)) {
                console.log(`✅ Panel ${panelId} verified successfully`);
                return { status: 'recovered' };
            }

            // Recreate the message
            const recreateResult = await this.recreatePanel(channel, panelId, panelData);
            return recreateResult;

        } catch (error) {
            console.error(`Error verifying panel ${panelId}:`, error);
            return { status: 'error', error: error.message };
        }
    }

    /**
     * Validate that a message matches the panel configuration
     * @param {Message} message - Discord message
     * @param {Object} panelData - Panel configuration
     * @returns {boolean} True if message is valid
     */
    validateMessage(message, panelData) {
        try {
            // Check if message has embeds
            if (!message.embeds || message.embeds.length === 0) {
                return false;
            }

            const embed = message.embeds[0];
            
            // Check embed title and description
            if (embed.title !== panelData.title || embed.description !== panelData.description) {
                return false;
            }

            // Check if message has components (buttons)
            if (!message.components || message.components.length === 0) {
                return false;
            }

            // Count buttons
            let buttonCount = 0;
            for (const actionRow of message.components) {
                buttonCount += actionRow.components.length;
            }

            // Check if button count matches role count
            if (buttonCount !== panelData.roles.length) {
                return false;
            }

            return true;

        } catch (error) {
            console.error('Error validating message:', error);
            return false;
        }
    }

    /**
     * Recreate a panel message
     * @param {TextChannel} channel - Discord channel
     * @param {string} panelId - Panel ID
     * @param {Object} panelData - Panel configuration
     * @returns {Promise<Object>} Recreation result
     */
    async recreatePanel(channel, panelId, panelData) {
        try {
            console.log(`🔄 Recreating panel ${panelId} in #${channel.name}`);

            // Create embed
            const embed = new EmbedBuilder()
                .setTitle(panelData.title)
                .setDescription(panelData.description)
                .setColor(panelData.color ? parseInt(panelData.color.replace('#', ''), 16) : 0x5865F2);

            if (panelData.image_url || panelData.imageUrl) {
                embed.setImage(panelData.image_url || panelData.imageUrl);
            }

            // Add footer if configured
            if (panelData.footerText) {
                embed.setFooter({
                    text: panelData.footerText,
                    iconURL: channel.guild.iconURL()
                });
            }

            // Create buttons
            const buttons = this.createRoleButtons(panelData.roles, panelData.buttonStyle);
            const actionRows = this.createActionRows(buttons);

            // Send new message
            const newMessage = await channel.send({
                embeds: [embed],
                components: actionRows
            });

            // Update panel data with new message ID
            await reactionRoleStorage.updatePanel(panelId, { message_id: newMessage.id });

            console.log(`✅ Panel ${panelId} recreated successfully`);
            return { status: 'recreated' };

        } catch (error) {
            console.error(`Error recreating panel ${panelId}:`, error);
            
            // If recreation fails, remove the panel
            await reactionRoleStorage.removePanel(panelId);
            return { status: 'removed', reason: 'Recreation failed' };
        }
    }

    /**
     * Create role buttons
     * @param {Array} roles - Array of role configurations
     * @param {number} globalButtonStyle - Global button style for the panel
     * @returns {Array} Array of button builders
     */
    createRoleButtons(roles, globalButtonStyle) {
        return roles.map(role => {
            return new ButtonBuilder()
                .setCustomId(`reaction_role_${role.role_id}`)
                .setLabel(role.label)
                .setStyle(role.buttonStyle || globalButtonStyle || ButtonStyle.Secondary);
        });
    }

    /**
     * Create action rows for buttons (max 5 buttons per row)
     * @param {Array} buttons - Array of button builders
     * @returns {Array} Array of action row builders
     */
    createActionRows(buttons) {
        const actionRows = [];
        
        for (let i = 0; i < buttons.length; i += 5) {
            const row = new ActionRowBuilder();
            const rowButtons = buttons.slice(i, i + 5);
            row.addComponents(rowButtons);
            actionRows.push(row);
        }
        
        return actionRows;
    }
}

module.exports = new ReactionRoleRecovery();
